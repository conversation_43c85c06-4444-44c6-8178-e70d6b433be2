import { useQuery } from "@tanstack/react-query";
import { useEffect, useRef, useState } from "react";
import { Helmet } from 'react-helmet';
import { Link, useNavigate } from "react-router-dom";
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunModal from "../../components/AbunModal/AbunModal";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { getRedditPostFinderQueries, makeApiRequest } from "../../utils/api";
import { pageURL } from "../routes";
import './RedditPostFinder.min.css';

import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import RedditPostFinderTable from "./RedditPostFinderTable";

interface RedditQuery {
    id: string,
    query: string,
    limit: number,
    is_processing: boolean,
    created_at: Date
}

function RedditPostFinder() {

    // -------------------------- STATES --------------------------
    const [queries, setQueries] = useState<Array<RedditQuery>>([]);
    const [topic, setTopic] = useState("")
    const [limit, setLimit] = useState(400);
    const navigate = useNavigate();
    const [rpfQueriesGenerated, setRpfQueriesGenerated] = useState(0);
    const [maxRpfAllowed, setMaxRpfAllowed] = useState(0);
    const [showWarning, setShowWarning] = useState(false);
    const [activeTab, setActiveTab] = useState("reddit-seo");


    // -------------------------- QUERIES --------------------------
    const {
        isFetching,
        isError,
        data,
        refetch
    } = useQuery({
        queryKey: ['getRedditPostFinderQueries'],
        queryFn: getRedditPostFinderQueries,
        refetchOnWindowFocus: false
    });


    // -------------------------- REFS --------------------------
    const errorAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);

    // ---------------------- EFFECTS ----------------------
    useEffect(() => {
        if (data) {
            setQueries(data['data']['queries']);
            setRpfQueriesGenerated(data.data.rpf_queries_generated);
            setMaxRpfAllowed(data.data.max_rpf_allowed);
        }
    }, [data]);

    // Creative Logic Implemented on keyword Click
    const handleSubmit = async (e: any) => {
        e.preventDefault();
        errorAlertRef.current?.close();
        // Check if the topic is empty
        if (!topic || !limit) {
            return;
        }
        try {
            const response = await makeApiRequest(
                `/api/frontend/post-reddit-finder-query/`,
                'post',
                {
                    query: topic,
                    limit: limit
                }
            );

            const data = response.data;
            if (data.success) {
                localStorage.setItem('task_id', data.task_id);
                successAlertRef.current?.show('Query submitted. It will take few minutes to complete.');
                navigate(`/reddit-seo/view/${data.id}/`);
            } else {
                if (data.reason === "max_limit_reached") {
                    errorAlertRef.current?.show('You have reached your maximum limit for this plan. Please Upgrade !');
                }
                else {
                    errorAlertRef.current?.show('Failed to fetch queries from backend API.');
                }
            }
        } catch (err) {
            errorAlertRef.current?.show('An error occurred while fetching GPF queries.');
        }
    }

    const handleProceedClick = async (e) => {
        const updatedLimit = Math.max(0, maxRpfAllowed - rpfQueriesGenerated);

        setLimit(updatedLimit); // Update limit
        setShowWarning(false); // Close the modal

        e.preventDefault();
        errorAlertRef.current?.close();
        // Check if the topic is empty
        if (!topic || !limit) {
            return;
        }
        try {
            const response = await makeApiRequest(
                `/api/frontend/post-reddit-finder-query/`,
                'post',
                {
                    query: topic,
                    limit: updatedLimit
                }
            );

            const data = response.data;
            if (data.success) {
                localStorage.setItem('task_id', data.task_id);
                successAlertRef.current?.show('Query submitted. It will take few minutes to complete.');
                navigate(`/reddit-seo/view/${data.id}/`);
            } else {
                if (data.reason === "max_limit_reached") {
                    errorAlertRef.current?.show('You have reached your maximum limit for this plan. Please Upgrade !');
                }
                else {
                    errorAlertRef.current?.show('Failed to fetch queries from backend API.');
                }
            }
        } catch (err) {
            errorAlertRef.current?.show('An error occurred while fetching gpf queries.');
        }
    };

    // ============================================================
    // --------------------- MAIN RENDER CODE ---------------------
    // ============================================================
    if (isError) {
        return (
            <section className="section">
                <div className="container">
                    <div className="box">
                        <h1 className="title has-text-centered">Find Blogs with Reddit Post
                            Opportunities for your Topic/Niche</h1>
                        <p className="has-text-centered is-size-5">
                            Failed to load data. Please try again later.
                        </p>
                    </div>
                </div>
            </section>
        );
    } else {
        return (
            <>
                <Helmet>
                    <title>Create Reddit SEO | Abun.com</title>
                    <meta
                        name="description"
                        content="Find High Ranking Reddit Posts from Google. Comment on the post to bring traffic & build links."
                    />
                </Helmet>
                <div className="reddit-container w-100">
                    <div className={""}>
                        {/* ------------ Reddit SEO Header ------------ */}
                        <div className="mb-5 is-flex-wrap-wrap reddit-header is-flex is-justify-content-space-between">
                            <div className={"is-flex is-justify-content-center is-flex-direction-column "}>
                                <h2>Reddit SEO</h2>
                                <p className={"reddit-p has-text-dark"}>Find Reddit posts that rank on Google for your target keyword. <br />
                                    Comment on these threads with helpful insights & a backlink to your site to<br />
                                    start getting free, targeted traffic from posts that already get search traffic.
                                </p>
                            </div>
                            <div className="mt-3">
                                <span className="is-block">
                                    3 Reddit SEO Projects remaining. <Link to={pageURL["subscriptionCredit"]} className="is-text has-text-black is-underlined" >View Credits</Link>
                                </span>
                                <div className="field is-hidden is-flex-wrap-wrap has-addons mt-2">
                                    <p className="control">
                                        <button className="button">
                                            <span className="icon is-small" style={{ fontSize: '0.72rem', marginRight: '0.4rem' }}>
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512">
                                                    <path d="M549.7 124.1c-6.3-23.7-24.8-42.3-48.3-48.6C458.8 64 288 64 288 64S117.2 64 74.6 75.5c-23.5 6.3-42 24.9-48.3 48.6-11.4 42.9-11.4 132.3-11.4 132.3s0 89.4 11.4 132.3c6.3 23.7 24.8 41.5 48.3 47.8C117.2 448 288 448 288 448s170.8 0 213.4-11.5c23.5-6.3 42-24.2 48.3-47.8 11.4-42.9 11.4-132.3 11.4-132.3s0-89.4-11.4-132.3zm-317.5 213.5V175.2l142.7 81.2-142.7 81.2z" />
                                                </svg>
                                            </span>
                                            <span className="has-text-black">How it Works Video</span>
                                        </button>
                                    </p>
                                    <p className="control">
                                        <button className="button">
                                            <span className="icon is-small" style={{ fontSize: '0.54rem', marginRight: '0.4rem' }}>
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                                                    <path d="M448 360V24c0-13.3-10.7-24-24-24H96C43 0 0 43 0 96v320c0 53 43 96 96 96h328c13.3 0 24-10.7 24-24v-16c0-7.5-3.5-14.3-8.9-18.7-4.2-15.4-4.2-59.3 0-74.7 5.4-4.3 8.9-11.1 8.9-18.6zM128 134c0-3.3 2.7-6 6-6h212c3.3 0 6 2.7 6 6v20c0 3.3-2.7 6-6 6H134c-3.3 0-6-2.7-6-6v-20zm0 64c0-3.3 2.7-6 6-6h212c3.3 0 6 2.7 6 6v20c0 3.3-2.7 6-6 6H134c-3.3 0-6-2.7-6-6v-20zm253.4 250H96c-17.7 0-32-14.3-32-32 0-17.6 14.4-32 32-32h285.4c-1.9 17.1-1.9 46.9 0 64z" />
                                                </svg>
                                                {/* <FontAwesomeIcon icon={faBook} className="ml-2 is-size-6"/> */}
                                            </span>
                                            <span className="has-text-black">How-To Guide</span>
                                        </button>
                                    </p>
                                    <p className="control">
                                        <button className="button">
                                            <span className="icon is-small" style={{ fontSize: '0.62rem', marginRight: '0.4rem' }}>
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                                    <path d="M256 32C114.6 32 0 125.1 0 240c0 49.6 21.4 95 57 130.7C44.5 421.1 2.7 466 2.2 466.5c-2.2 2.3-2.8 5.7-1.5 8.7S4.8 480 8 480c66.3 0 116-31.8 140.6-51.4 32.7 12.3 69 19.4 107.4 19.4 141.4 0 256-93.1 256-208S397.4 32 256 32zM128 272c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32zm128 0c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32zm128 0c-17.7 0-32-14.3-32-32s14.3-32 32-32 32 14.3 32 32-14.3 32-32 32z" />
                                                </svg>
                                            </span>
                                            <span className="has-text-black">Support</span>
                                        </button>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="tabs is-medium" style={{ scrollbarWidth: 'none' }}>
                            <ul>
                                <li className={activeTab === "reddit-seo" ? "is-active" : ""}>
                                    <a onClick={() => setActiveTab("reddit-seo")}>Reddit SEO</a>
                                </li>
                                <li className={activeTab === "projects" ? "is-active" : ""}>
                                    <a onClick={() => setActiveTab("projects")}>Projects</a>
                                </li>
                            </ul>
                        </div>

                        {activeTab === "reddit-seo" &&
                            <>
                                <div className={"is-flex is-align-items-center is-flex-direction-column reddit-post-container"}>
                                    <h3 >Let's find the Reddit posts for target keyword</h3>
                                    <hr />
                                    <form className="reddit-form w-100"
                                        onSubmit={(e) => {
                                            e.preventDefault(); // Prevent the default form submission behavior
                                            const remaining = maxRpfAllowed - rpfQueriesGenerated;
                                            if (limit > remaining) {
                                                setShowWarning(true);
                                            } else {
                                                handleSubmit(e);
                                            }

                                        }}>
                                        <div className="field">
                                            <label className="ca-label has-text-black label">Your Target Keyword</label>
                                            <div className="control">
                                                <input
                                                    className="input"
                                                    type="text"
                                                    placeholder="AI article generators"
                                                    value={topic}
                                                    onChange={(e) => setTopic(e.target.value)}
                                                    // style={{ marginTop: '3px', width: "60%" }}
                                                    required
                                                />
                                            </div>
                                        </div>
                                        <div className="field" style={{ display: 'none' }}>
                                            <label className="ca-label has-text-black label">How many max posts to
                                                find? (It should be between 10-400)</label>
                                            {/* <span className="text-muted"> </span><br /> */}
                                            <input
                                                className="ca-input input mb-3"
                                                type="number"
                                                placeholder="Enter limit"
                                                value={limit}
                                                onChange={(e) => {
                                                    const value = parseInt(e.target.value);
                                                    if (isNaN(value) || (value > 0 && value <= 400)) {
                                                        setLimit(value);
                                                    }
                                                }}
                                                min={1}
                                                max={400}
                                                style={{ marginTop: '3px', textAlign: 'center', width: '30%' }}
                                                required
                                            />
                                        </div>
                                        <button type={"submit"} className="mt-2 button is-responsive is-link" style={{ width: 'fit-content' }}
                                            disabled={isFetching || queries.some((query) => query.query === topic && query.is_processing) || (topic.trim() === "")}>
                                            PROCEED <FontAwesomeIcon icon={faArrowRight} className="ml-2 is-size-6" />
                                        </button>
                                    </form>
                                </div>
                                <SuccessAlert ref={successAlertRef} />
                                <ErrorAlert ref={errorAlertRef} />

                                {/************************* Warning Alert ***************************/}
                                <AbunModal
                                    active={showWarning}
                                    headerText={"Usage Limit Alert"}
                                    closeable={false}
                                    hideModal={() => setShowWarning(false)}>
                                    <div>
                                        <p className={"has-text-centered mt-4"}>
                                            You have only {Math.max(0, maxRpfAllowed - rpfQueriesGenerated)} searches left in your account.<br />Do you want to proceed?
                                        </p>
                                        <div className={"mt-6 has-text-centered is-flex is-justify-content-center is-align-items-center"}>
                                            <AbunButton type={"danger"} clickHandler={handleProceedClick} disabled={Math.max(0, maxRpfAllowed - rpfQueriesGenerated) === 0}>PROCEED</AbunButton>
                                            <AbunButton type={"primary"} className={"ml-4"} clickHandler={() => setShowWarning(false)}>CANCEL</AbunButton>
                                        </div>
                                    </div>
                                </AbunModal>
                            </>

                        }

                        {activeTab === "projects" &&
                            <RedditPostFinderTable />
                        }
                    </div>
                </div>
            </>
        );
    }
}

export default RedditPostFinder;
