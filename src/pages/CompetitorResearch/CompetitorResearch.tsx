/* eslint-disable react-hooks/exhaustive-deps */
import Autocomplete from '@mui/material/Autocomplete';
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import { useMutation, useQuery } from "@tanstack/react-query";
import { ColumnDef, createColumnHelper, RowData, RowModel } from "@tanstack/react-table";
import { useContext, useEffect, useMemo, useRef, useState } from "react";
import { Helmet } from 'react-helmet';
import { Link, useLoaderData, useNavigate, useRouteLoaderData } from "react-router-dom";
import EarthFlag from '../../assets/images/earth-flag.webp';
import AbunButton from "../../components/AbunButton/AbunButton";
import AbunLoader from '../../components/AbunLoader/AbunLoader';
import AbunModal from "../../components/AbunModal/AbunModal";
import AbunTable from "../../components/AbunTable/AbunTable";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import Icon from "../../components/Icon/Icon";
import LoadingData from "../../components/LoadingData/LoadingData";
import LoadingError from "../../components/LoadingError/LoadingError";
import SuccessAlert from '../../components/SuccessAlert/SuccessAlert';
import Survey from "../../components/Survey/Survey";
import TextArea from "../../components/TextArea/TextArea";
import { useUIState } from "../../hooks/UIStateContext";
import { BasePageData } from "../../pages/Base/Base";
import { SurveyContext } from "../../pages/Login/SurveyContext";
import { addCompetitors, checkJobStatus, competitorsQuery, generateCompetitorKeywords, getCompResearchData, removeCompetitors, retryFn } from "../../utils/api";
import countries from "../../utils/constants/CountriesforSerp";
import { getDefaultCompetitorLogo } from "../../utils/misc";
import { PageData } from "../KeywordsResearchV2/KeywordResearch";
import { pageURL } from "../routes";
import './CompetitorResearch.min.css';

interface CompetitorData {
	domain: string
	keywords_generated: boolean
	in_processing: boolean
	keyword_project_id?: string
	organic_traffic?: string | null
	organic_keywords?: string | null
	domain_authority?: string | null
	total_backlinks?: string | null
	follow?: string | null
	no_follow?: string | null
	referring_domains?: string | null
	current_plan_name: string;
}

interface TableRow {
	logo: string
	domain: string
}

interface CountryType {
	location_code: number;
	location_name: string;
	country_iso_code: string;
	suggested?: boolean;
}


export default function CompetitorResearch() {
	// ---------------------- NON-STATE CONSTANTS ----------------------
	const pageSizes = [30, 100, 500]

	// ---------------------- STATES ----------------------
	const pageData = useLoaderData() as PageData
	const basePageData: BasePageData = useRouteLoaderData("base") as BasePageData;
	const { hamburgerActive } = useUIState();

	const [
		competitorData,
		setCompetitorData
	] = useState<CompetitorData[]>([]);

	const [
		generatingKeywordsModalActive,
		setGeneratingKeywordsModalActive
	] = useState(false);

	const [
		selectedRows,
		setSelectedRows
	] = useState<RowModel<RowData>>();

	const [
		showAddCompetitorsModal,
		setShowAddCompetitorsModal
	] = useState<boolean>(false);

	const [
		domainTextArea,
		setDomainTextArea
	] = useState<string>("");

	const [
		bulkDeleteConfirmationModalActive,
		setBulkDeleteConfirmationModalActive
	] = useState<boolean>(false);

	const [
		deleteCompetitorModel,
		setdeleteCompetitorModel
	] = useState<boolean>(false);

	const [
		kwProjectId,
		setKwProjectId
	] = useState("")

	const [
		disableAddCompetitorsButton,
		setDisableAddCompetitorsButton
	] = useState<boolean>(false);

	const [
		showAddLocationsModal,
		setShowAddLocationsModal
	] = useState<boolean>(false);

	const [
		selectedCompetitor,
		setSelectedCompetitor
	] = useState<string>("");

	const [
		selectedLocation,
		setSelectedLocation
	] = useState<CountryType>({
		"location_code": 1,
		"location_name": "Global",
		"country_iso_code": "ZZ"
	});
	const [jobRunning, setJobRunning] = useState<boolean>(() => {
		const storedValue = localStorage.getItem(`${basePageData.active_website_domain}JobRunning`);
		return storedValue ? JSON.parse(storedValue) : false;
	});
	const [modalActive, setModalActive] = useState(false);
	const [jobId, setJobId] = useState("");
	const [regenerateCompetitor, setRegenerateCompetitor] = useState(false)
	const [disableRegenerate, setDisableRegenerate] = useState(pageData.regenerate_competitor)
	const [isJobCompleted, setIsJobCompleted] = useState(false);
	const [deleteDomain, setDeleteDomain] = useState("")

	// -------------------------- SURVEY CONTEXT --------------------------
	const context = useContext(SurveyContext);

	// ----------------------- REFS -----------------------
	const failAlertRef = useRef<any>(null);
	const successAlertRef = useRef<any>(null);
	const navigate = useNavigate();

	// ---------------------- QUERIES ----------------------
	const competitorsFetch = useQuery({
		queryKey: ['getCompResearchData'],
		queryFn: getCompResearchData,
		refetchOnWindowFocus: false,
		retry: retryFn
	});

	// ---------------------- MUTATIONS ----------------------
	const generateKeywords = useMutation({
		mutationKey: ['generateCompetitorKeywords'],
		mutationFn: generateCompetitorKeywords,
		cacheTime: 0,
		onSuccess: () => {
			failAlertRef.current?.close();
			setGeneratingKeywordsModalActive(false);
			setTimeout(() => {
				competitorsFetch.refetch().then();
				setTimeout(() => {
					competitorsFetch.refetch().then();
				}, 6000);
			}, 1000);
		},
		onError: (error: Error) => {
			setGeneratingKeywordsModalActive(false);
			failAlertRef.current?.show(error.message);
			setTimeout(() => {
				failAlertRef.current?.close();
			}, 5000);
		}
	});

	const removeCompetitorsMut = useMutation({
		mutationKey: ['removeCompetitors'],
		mutationFn: removeCompetitors,
		cacheTime: 0,
		retry: retryFn,
		onError: (error) => {
			console.error(error);
			successAlertRef.current?.close();
			if (pageData.current_plan_name !== "Trial") {
				failAlertRef.current?.show("Server Error. Please try again in some time.");
			}
		}
	});

	const addCompetitorsMut = useMutation({
		mutationKey: ['addCompetitors'],
		mutationFn: addCompetitors,
		cacheTime: 0,
		retry: retryFn,
		onError: (error) => {
			console.error(error);
			successAlertRef.current?.close();
			if (pageData.current_plan_name !== "Trial") {
				failAlertRef.current?.show("Server Error. Please try again in some time.");
			}
		}
	})

	let domain: string = basePageData.active_website_domain ?? '';
	const CompetitorsMut = useMutation({
		mutationKey: ['competitors'],
		mutationFn: competitorsQuery,
		cacheTime: 0,
		retry: retryFn,
		onError: (error) => {
			console.error(error);
			successAlertRef.current?.close();
			if (pageData.current_plan_name !== "Trial") {
				failAlertRef.current?.show("Server Error. Please try again in some time.");
			}
		}
	})

	const CompetitorFinderQuery = checkJobStatus(jobId);

	const { data: jobStatus } = useQuery({
		...CompetitorFinderQuery,
		refetchInterval: 5000,
		enabled: !!jobId && !isJobCompleted,
		retry: retryFn,
		onSuccess: async (result: any) => {
			if (result.data.status === "completed") {
				setModalActive(false);
				setJobRunning(false)
				setJobId("")
				setIsJobCompleted(true);
				competitorsFetch.refetch().then();
			}
		},
		onError: (error) => {
			setJobId("");
			setJobRunning(false)
			setModalActive(false);
		},
	});
	// ---------------------- EFFECTS ----------------------
	useEffect(() => {
		localStorage.setItem(`${basePageData.active_website_domain}JobRunning`, JSON.stringify(jobRunning));
	}, [jobRunning]);

	useEffect(() => {
		if (competitorsFetch.data) {
			const backBtn = document.querySelector(".back-btn.steal-comp-keywords");
			if (backBtn && (backBtn.parentNode?.firstChild as HTMLElement)?.tagName === "ARTICLE") {
				backBtn.classList.add("warning-is-present");
			}
			if (competitorsFetch.data.data === "No website found") {
				setDisableAddCompetitorsButton(true);
			} else {
				setCompetitorData(competitorsFetch.data['data']);
				if (competitorsFetch.data['data'].length > 0 && competitorsFetch.data['data'][0]["content_plan_generation_status"] === "done") {
					setDisableAddCompetitorsButton(false);
				}
			}
		}
	}, [competitorsFetch.data]);

	useEffect(() => {
		// Find the country that matches the `country_iso_code` with `pageData.country_code`
		if (pageData.country_code !== "ZZ") {
			const matchedCountry = countries.find(
				(country) => country.country_iso_code === pageData.country_code.toUpperCase()
			);
			// If a match is found, update the selected location
			if (matchedCountry) {
				setSelectedLocation(matchedCountry);
			}
		}
	}, [pageData.country_code]);

	const hasFetched = useRef(false);

	useEffect(() => {
		if (!hasFetched.current) {
			handleButtonClick();
			hasFetched.current = true;
		}
	}, []);
	const { showSurvey } = context ? context : { showSurvey: false }

	// ------------------------FUNCTIONS -----------------------------
	const handleButtonClick = async (updateRegenerateCompetitor?: boolean) => {
		const regenerateCompetitor = updateRegenerateCompetitor ?? false;
		setRegenerateCompetitor(regenerateCompetitor);

		try {
			setModalActive(true);
			const response = await competitorsFetch.refetch();

			if (!updateRegenerateCompetitor && response.data && response.data.data && response.data.data !== "No website found" && response.data.data.length !== 0) {
				setModalActive(false);
				return;
			}

			if (
				(updateRegenerateCompetitor === true && !jobRunning) ||
				(updateRegenerateCompetitor === undefined && (!competitorData || competitorData.length === 0) && !jobRunning)
			) {
				setJobRunning(true);
				setDisableRegenerate(true)
				const competitorRefetch = await CompetitorsMut.mutateAsync({
					protocol: "https",
					domain: domain,
					regenerateCompetitor: updateRegenerateCompetitor,
				});

				if (competitorRefetch.data && competitorRefetch['data']) {
					const jobId = competitorRefetch['data']['job_id'];
					setJobId(jobId);
				} else {
					setModalActive(false);
					setJobRunning(false);
				}
			} else {
				setModalActive(false);
				setJobRunning(false);
			}
		} catch (error) {
			setModalActive(false);
			setJobRunning(false);
		}
	};

	function selectedRowsSetter(rowModel: RowModel<RowData>) {
		setSelectedRows(rowModel);
	}

	function addCompetitorsHandler() {
		let domains: Array<string> = domainTextArea.split("\n");
		if (domains.length > 0) {
			domains = domains.map(value => value.toLowerCase().trim());
			addCompetitorsMut.mutate({ domains: domains }, {
				onSuccess: () => {
					// Close modal
					setShowAddCompetitorsModal(false);
					setDomainTextArea("");

					// Modify (table) data
					competitorsFetch.refetch().then();

					failAlertRef.current?.close();
					successAlertRef.current?.show("Competitor(s) have been added successfully!");
				}
			})
		}
	}

	function removeCompetitorsHandler(domain: string, projectId: string) {
		removeCompetitorsMut.mutate({ domain: domain, projectId: projectId }, {
			onSuccess: (response) => {
				// Uncheck checkboxes
				// selectedRows?.rows.forEach(row => {
				// 	row.toggleSelected()
				// });				
				// update table data
				const successMessage = response?.data?.message || "Competitor removed successfully!";

				setdeleteCompetitorModel(false);
				setKwProjectId("")
				setDeleteDomain("")
				failAlertRef.current?.close();
				if (response.data.message.includes("Article or Auto Blog")) {
					failAlertRef.current?.show(response.data.message);
				} else {
					const updatedCompetitors = competitorData.filter((competitor) => !domain.includes(competitor.domain));
					setCompetitorData(updatedCompetitors);
					successAlertRef.current?.show(response.data.message);
				}
			},
			onError: (error) => {
				const errorMessage = error?.response?.data?.message || "Failed to remove competitor. Please try again.";
				failAlertRef.current?.show(errorMessage);

			}
		});
	}

	function handleCompetitorDelete(domain: string, projectId: string) {
		setdeleteCompetitorModel(true)
		setDeleteDomain(domain);
		setKwProjectId(projectId)
	}


	// ---------------------- TABLE DATA ----------------------
	const columnHelper = createColumnHelper<CompetitorData>();

	const columnDefs: ColumnDef<any, any>[] = [
		columnHelper.accessor((row: CompetitorData) => row.domain, {
			id: 'domain',
			header: "Competitor",
			cell: useMemo(() => info => {
				const rowData = info.row.original;
				let cellValue = info.getValue();

				if (cellValue === null) return "---";

				if (cellValue.length > 25) {
					cellValue = cellValue.substring(0, 24) + "...";
				}

				// Wrap in Link if keywords are generated
				if (rowData.keywords_generated && rowData.keyword_project_id) {
					return (

						<Link
							to={pageURL['keywordResearch'] + "?keywordProjectId=" + rowData.keyword_project_id}
							className={"has-text-black"}
						>
							<div className="is-flex is-align-items-center">
								<div className="is-inline-block">
									{cellValue}
								</div>
							</div>
						</Link>
					);
				} else {
					// Show "Generate Keywords" button
					return (
						<div className="is-flex is-align-items-center">
							<div className="is-inline-block">
								{cellValue}
							</div>
						</div>
					);

				}
			}, []),
			enableGlobalFilter: true,
		}),
		columnHelper.display({
			id: 'generate',
			header: "Generate Competitor Keywords Rankings",
			cell: props => {
				if (props.row.original.in_processing) {
					return (
						<div className="ml-6">
							<AbunButton type={"primary"}
								className={"is-outlined ml-6 is-small comp-research-table-button"}
								disabled={true}
								clickHandler={() => { }}>
								Generating...
							</AbunButton>
						</div>
					)

				}
				else if (props.row.original.keywords_generated && props.row.original.keyword_project_id) {
					return (<div className="ml-6">
						<Link to={
							pageURL['keywordResearch'] + "?keywordProjectId=" + props.row.original.keyword_project_id
						}
							className={"button ml-6 is-primary is-small comp-research-table-button"}>
							View Keywords
						</Link>
					</div>
					)

				} else {
					return (<div className="ml-6">
						<AbunButton type={"success"}
							className={"is-outlined ml-6 is-small comp-research-table-button"}
							disabled={generateKeywords.isLoading}
							clickHandler={() => {
								setShowAddLocationsModal(true);
								setSelectedCompetitor(props.row.original.domain);
							}}>
							Generate Keywords
						</AbunButton>
					</div>
					)
				}
			},
			meta: {
				align: "left"
			}
		}),
		columnHelper.display({
			id: 'delete',
			header: "Action",
			cell: (props) => (
				<div style={{ textAlign: 'center' }}>
					<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg"
						onClick={() => {
							const keywordProjectId: string = props.row.original.keyword_project_id ?? "";
							handleCompetitorDelete(props.row.original.domain, keywordProjectId);
						}}>
						<g clip-path="url(#clip0_48_5565)">
							<g clip-path="url(#clip1_48_5565)">
								<path d="M3.15356 6.32313C3.44461 10.8562 3.72319 13.2144 3.88856 14.3369C3.97256 14.9046 4.34531 15.3672 4.90346 15.5011C5.66306 15.6839 6.9713 15.8906 9.00075 15.8906C11.0302 15.8906 12.3381 15.6839 13.098 15.5014C13.6559 15.3676 14.0286 14.9049 14.1126 14.3373C14.2783 13.2144 14.5566 10.8562 14.8476 6.32214" stroke="black" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round" />
								<path d="M11.3087 3.47962C12.4769 3.50128 13.3871 3.53672 14.0394 3.56986C14.8236 3.60923 15.552 4.02694 15.7712 4.78097C15.804 4.89417 15.8349 5.01394 15.8618 5.14092C15.9911 5.74467 15.5392 6.26344 14.924 6.31561C13.9331 6.39928 12.1195 6.49444 8.99249 6.49444C5.86579 6.49444 4.05191 6.39928 3.0613 6.31561C2.44574 6.26377 1.99129 5.74139 2.15043 5.14486C2.20785 4.92994 2.2784 4.73372 2.35255 4.55948C2.61932 3.93506 3.26146 3.61284 3.93937 3.57544C4.56543 3.54131 5.47663 3.50259 6.69135 3.47962C6.87108 3.07198 7.16548 2.7254 7.53869 2.48211C7.9119 2.23882 8.34781 2.10932 8.79332 2.10938H9.20741C9.65286 2.10938 10.0887 2.23891 10.4618 2.4822C10.835 2.72549 11.129 3.07203 11.3087 3.47962Z" stroke="black" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round" />
								<path d="M7.03125 9.32812L7.35937 12.6094" stroke="black" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round" />
								<path d="M10.9687 9.32812L10.6406 12.6094" stroke="black" stroke-width="1.05" stroke-linecap="round" stroke-linejoin="round" />
							</g>
						</g>
						<defs>
							<clipPath id="clip0_48_5565">
								<rect width="16.8" height="16.8" fill="white" transform="translate(0.600098 0.599976)" />
							</clipPath>
							<clipPath id="clip1_48_5565">
								<rect width="16.8" height="16.8" fill="white" transform="translate(0.600098 0.599976)" />
							</clipPath>
						</defs>
					</svg>
				</div>
			),
			meta: {
				align: "center"
			}
		}),

	]

	// ==============================================================
	// ---------------------- MAIN RENDER CODE ----------------------
	// ==============================================================

	if (competitorsFetch.isFetching) return <LoadingData />

	if (competitorsFetch.isError) return <LoadingError />

	return (
	<>
	   <Helmet>
			<title>Steal Competitor Keywords | Abun.com</title>
			<meta
			 name="description"
			 content="Find and use keywords your competitors are already ranking for."/>
		</Helmet>
		<div className={"competitor-wrapper " + (pageData.current_plan_name === "Trial" ? "trial-user-overlay" : "")}>
			<div className="content-container">
				{/* Main content area with conditional blur */}
				<div className={pageData.current_plan_name === "Trial" ? "competitor-blur-wrapper" : ""}>

					<div className={"w-100"}>
						{showSurvey && <Survey />}
						{(regenerateCompetitor === true || (regenerateCompetitor === false && basePageData.competitor_length === 0)) && (
							<AbunModal
								active={modalActive}
								headerText=""
								closeable={false}
								hideModal={() => setModalActive(false)}
							>
								<div className="loadingData w-100 is-flex is-justify-content-center is-align-items-center is-flex-direction-column">
									<AbunLoader show={modalActive} height="20vh" />
									<div style={{ fontSize: '1.3em' }}>Processing request. Please wait...</div>
								</div>
							</AbunModal>
						)}
						<AbunModal active={generatingKeywordsModalActive}
							headerText={""}
							closeable={false} hideModal={() => setGeneratingKeywordsModalActive(false)}>
							<div className={"loadingData w-100 is-flex is-justify-content-center is-align-items-center"}>
								<AbunLoader show={generatingKeywordsModalActive} height="20vh" />
							</div>
							<p className={"is-size-4 has-text-centered mb-4"}>
								Generating keywords. Please wait until it's done.
							</p>
						</AbunModal>
						<AbunModal active={showAddCompetitorsModal}
							headerText={""}
							closeable={true}
							closeableKey={true}
							hideModal={() => setShowAddCompetitorsModal(false)}>
							<h4 className={"is-size-4 mt-2 has-text-centered font-secondary has-text-primary has-text-weight-bold"}>
								Enter Competitors
							</h4>
							<p className={"has-text-centered"}>
								You can add both full URLs (ex: <b>https://example.com/blog/</b>) or only registered
								domain (ex: <b>example.com</b>). Invalid values will be ignored.
							</p>
							<TextArea value={domainTextArea}
								className={"mt-4"}
								placeholder={"Enter each competitor on a new line..."}
								rows={15}
								onChange={setDomainTextArea} />
							<AbunButton type={"success"}
								className={"mt-5 is-block ml-auto mr-auto"}
								disabled={!domainTextArea.length || addCompetitorsMut.isLoading}
								clickHandler={addCompetitorsHandler}>
								<Icon iconName={"floppy-disk"} additionalClasses={["icon-white"]} />
								&nbsp;&nbsp;{addCompetitorsMut.isLoading ? "Saving..." : "Save Competitors"}
							</AbunButton>
						</AbunModal>
						<div className="card-container">
							<div className="is-flex is-align-items-center w-100 is-flex-wrap-wrap" style={{position:'relative'}}>
								<svg className={"is-clickable"} onClick={() => navigate(pageURL['keywordResearch'])} width="28" height="24" viewBox="0 0 28 24" style={{marginBottom:'-24px'}}>
							        <path d="M27.5 12H2M2 12L13 1M2 12L13 23" stroke="black" strokeOpacity="0.5" strokeWidth="2" />
						        </svg>
								
								<h2 className="w-100 ml-5 has-text-centered">Steal Competitor Keywords</h2>

								<span className="is-block mt-2 usage-credit w-100">
									3 Steal Competitor Keyword Credits remaining. <Link to={pageURL["subscriptionCredit"]} className="is-text has-text-black is-underlined" >View Credits</Link>
								</span>
							</div>
							<AbunTable tableContentName={"Competitors"}
								tableData={competitorData}
								columnDefs={columnDefs}
								pageSizes={pageSizes}
								initialPageSize={pageSizes[2]}
								noDataText={"No Competitors Found"}
								searchboxPlaceholderText={"Search Competitors..."}
								id="competitor-research-table"
								buttonStyle={{ display: "flex" }}
								buttons={[
									{
										text: (
											<>
												<svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '0.2rem', verticalAlign: 'middle', marginBottom: '0.1rem', height: 'auto', width: '16px', stroke: '#fff' }}>
													<path d="M15.75 9.5C15.75 9.64918 15.6907 9.79226 15.5852 9.89775C15.4798 10.0032 15.3367 10.0625 15.1875 10.0625H9.5625V15.6875C9.5625 15.8367 9.50324 15.9798 9.39775 16.0852C9.29226 16.1907 9.14918 16.25 9 16.25C8.85082 16.25 8.70774 16.1907 8.60225 16.0852C8.49676 15.9798 8.4375 15.8367 8.4375 15.6875V10.0625H2.8125C2.66332 10.0625 2.52024 10.0032 2.41475 9.89775C2.30926 9.79226 2.25 9.64918 2.25 9.5C2.25 9.35082 2.30926 9.20774 2.41475 9.10225C2.52024 8.99676 2.66332 8.9375 2.8125 8.9375H8.4375V3.3125C8.4375 3.16332 8.49676 3.02024 8.60225 2.91475C8.70774 2.80926 8.85082 2.75 9 2.75C9.14918 2.75 9.29226 2.80926 9.39775 2.91475C9.50324 3.02024 9.5625 3.16332 9.5625 3.3125V8.9375H15.1875C15.3367 8.9375 15.4798 8.99676 15.5852 9.10225C15.6907 9.20774 15.75 9.35082 15.75 9.5Z" fill="white"></path>
												</svg>
												Add Competitors
											</>
										),
										// type: "success",
										type: "primary",
										isDisabled: disableAddCompetitorsButton,
										clickHandler: () => setShowAddCompetitorsModal(true),
										extraClassName: "is-small is-justify-content-space-between",
										// iconName: "plus",
										// iconClasses: ["icon-white"]
									},
									...(!disableRegenerate
										? [{
											text: "Regenerate Competitors",
											type: "primary" as const,
											isDisabled: disableRegenerate,
											clickHandler: () => {
												// setRegenerateCompetitor(true);
												handleButtonClick(true);
											},
											extraClassName: "is-small is-justify-content-space-between ml-4",
											// iconName: "plus",
											iconClasses: ["icon-white"]
										}]
										: []
									)
								]}
							/>
						</div>
						{/* ------------------------- Delete Competitor Model ------------------------- */}
						<AbunModal active={deleteCompetitorModel}
							headerText={"Delete Project"}
							closeable={false}
							hideModal={() => {
								setdeleteCompetitorModel(false);
							}}>
							<div>
								<p className={"is-size-4 has-text-centered"}>Are You Sure? want to delete this competitor {domain} </p>
								<div className={"is-flex is-flex-direction-row is-justify-content-center mt-6 has-text-centered"}>
									<AbunButton type={"danger"} clickHandler={() => {
										if (deleteDomain) {
											removeCompetitorsHandler(deleteDomain as string, kwProjectId as string);
										}
									}}>
										Yes, Delete
									</AbunButton>
									<AbunButton type={"primary"} className={"ml-4"} clickHandler={() => {
										setdeleteCompetitorModel(false);
									}}>
										Cancel
									</AbunButton>
								</div>
							</div>
						</AbunModal>
						<div className={"blur-background " + (bulkDeleteConfirmationModalActive ? "" : "hidden")}>
							<div className={"confirmation-card w-100 mt-4"}>
								<button className={"delete is-pulled-right"}
									onClick={() => {
										setBulkDeleteConfirmationModalActive(false);
										selectedRows?.rows.forEach(row => {
											row.toggleSelected()
										});
									}} />
								<div className={"confirmation-card-content w-100"}>
									<h3 className={"is-size-4 has-text-centered"}>
										Are you sure you want to remove the selected competitors?
									</h3>
									<div className={"is-flex is-justify-content-center"}>
										<AbunButton type={"primary"}
											className={"mt-5 is-block ml-auto mr-auto go-back-button"}
											clickHandler={() => {
												setBulkDeleteConfirmationModalActive(false);
											}}>
											&nbsp;&nbsp;Go Back
										</AbunButton>
										<AbunButton type={"danger"}
											className={"mt-5 is-block ml-auto mr-auto"}
											clickHandler={() => {
												// Fetch the row's domain values and call delete function.
												let domains = selectedRows?.rows.map(row => {
													return (row.original as TableRow).domain;
												});
												if (domains) {
													// removeCompetitorsHandler(domains as Array<string>);
													setSelectedRows(undefined);
													setBulkDeleteConfirmationModalActive(false);
												}
											}}>
											<Icon iconName={"trash"} additionalClasses={["icon-white"]} />
											&nbsp;&nbsp;Yes, Remove
										</AbunButton>
									</div>
								</div>
							</div>
						</div>
						<div className={"blur-background " + (showAddLocationsModal ? "" : "hidden")}>
							<div className={"confirmation-card w-100 mt-4"}>
								<button className={"delete is-pulled-right"}
									onClick={() => {
										setShowAddLocationsModal(false);
										selectedRows?.rows.forEach(row => {
											row.toggleSelected()
										});
									}} />
								<div className={"confirmation-card-content w-100"}>
									<h3 className={"is-size-5 has-text-centered"}>
										Select the location you want to get keywords volume for:
									</h3>
									<div className={"comp-research-location-select"}>
										<div className={"comp-info"}>
											<img
												src={`${process.env['REACT_APP_LOGO_URL']}/${selectedCompetitor}`}
												onError={({ currentTarget }) => {
													currentTarget.onerror = null;
													currentTarget.src = getDefaultCompetitorLogo();
												}}
												className={"comp-logo"}
												alt={"logo"}
											/>
											<h4 className={"is-size-4 has-text-centered font-secondary"}>
												{selectedCompetitor}
											</h4>
										</div>
										<div className={"form-group location-select"}>
											<Autocomplete
												id="country-select-demo"
												sx={{ width: 300 }}
												options={countries}
												value={selectedLocation}
												autoHighlight
												getOptionLabel={(option) => option.country_iso_code !== "ZZ" ? `${option.location_name} (${option.country_iso_code})` : option.location_name}
												isOptionEqualToValue={(option, value) => option.location_code === value.location_code}
												renderOption={(props, option) => (
													<Box component="li" sx={{ '& > img': { mr: 2, flexShrink: 0 } }} {...props}>
														<img
															loading="lazy"
															width="20"
															srcSet={option.country_iso_code !== "ZZ" ? `https://flagcdn.com/w40/${option.country_iso_code.toLowerCase()}.png 2x` : EarthFlag}
															src={option.country_iso_code !== "ZZ" ? `https://flagcdn.com/w20/${option.country_iso_code.toLowerCase()}.png` : EarthFlag}
															alt=""
														/>
														{option.location_name} ({option.country_iso_code})
													</Box>
												)}
												renderInput={(params) => (
													<TextField
														{...params}
														label="Location"
														inputProps={{
															...params.inputProps,
															// disable autocomplete and autofill and suggestion
															autoComplete: 'off',
														}}
													/>
												)}
												onChange={(event, option) => {
													if (option) {
														setSelectedLocation(option);
													}
												}}
											/>
										</div>
										<AbunButton type={"primary"}
											className={"mt-5 is-block ml-auto mr-auto"}
											clickHandler={() => {
												if (selectedCompetitor) {
													generateKeywords.mutate({
														domain: selectedCompetitor,
														selectedLocation: selectedLocation,
													});
													setGeneratingKeywordsModalActive(true);
													setShowAddLocationsModal(false);
												}
											}}>Proceed
										</AbunButton>
									</div>
								</div>
							</div>
						</div>
						<SuccessAlert ref={successAlertRef} />
						<ErrorAlert ref={failAlertRef} />
					</div>

				</div>

				{!hamburgerActive && pageData.current_plan_name === "Trial" && (
					<div className="c-upgrade-modal">
						<h3>Upgrade to Unlock Competitor keywords.</h3>
						<p>Upgrade your plan to access all features.</p>
						<button onClick={() => navigate(pageURL["manageSubscription"])}>
							See Plans →
						</button>
					</div>
				)}
			</div>
		</div>
	</>
	);
}





