import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useMediaQuery } from '@mui/material';
import TextField from '@mui/material/TextField';
import { useMutation } from "@tanstack/react-query";
import React, { useContext, useRef, useState } from "react";
import { Helmet } from 'react-helmet';
import { Link, useNavigate, useRouteLoaderData } from "react-router-dom";
import { Tooltip } from 'react-tooltip';
import AbunModal from "../../components/AbunModal/AbunModal";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import Icon from "../../components/Icon/Icon";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import Survey from "../../components/Survey/Survey";
import { SurveyContext } from "../../pages/Login/SurveyContext";
import { generateSEOTitlesQueryFn } from "../../utils/api";
import type { CountryType } from "../../utils/constants/CountriesforSerp";
import { BasePageData } from "../Base/Base";
import { pageURL } from "../routes";
import './ProgrammaticSeo.min.css';
import TitleProject from "./TitleProject";

const ProgrammaticSeo = () => {
    const basePageData: BasePageData = useRouteLoaderData("base") as BasePageData;

    // --------------------------- STATES ---------------------------
    const [nTitles, setNTitles] = useState("");
    const [pattern, setPattern] = useState("");
    const [examples, setExamples] = useState(["", "", ""]);
    const [showLoading, setShowLoading] = useState(false);
    const successAlertRef = useRef<any>(null);
    const errorAlertRef = useRef<any>(null);
    const [isValid, setIsValid] = useState<boolean>(true);
    const [isPatternValid, setIsPatternValid] = useState<boolean>(true);
    const [hasTouchedExamples, setHasTouchedExamples] = useState<boolean[]>([]);
    const [isSpinner, setIsSpinner] = useState(false)
    const [isVerified, _] = useState(basePageData.user_verified || basePageData.user_has_ltd_plans);
    const [selectedLocation, setSelectedLocation] = useState<CountryType>({
        location_code: 1,
        location_name: 'Global',
        country_iso_code: 'ZZ',
    });
    const [activeTab, setActiveTab] = useState("programmatic-seo");
    // -------------------------- SURVEY CONTEXT --------------------------
    const context = useContext(SurveyContext);
    const { showSurvey } = context ? context : { showSurvey: false }
    const isSmallScreen = useMediaQuery('(max-width:490px)');
    const examplePlaceholders = [
        "Hire Graphic Designer in San Francisco",
        "Hire Logo Designer in San Francisco",
        "Hire Brochure Designer in San Francisco",
    ];

    const generateSeoTitles = useMutation(generateSEOTitlesQueryFn)

    const navigate = useNavigate();


    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setNTitles(value);

        // Validate the input: it should be a positive number
        if (Number(value) > 0 && Number(value) <= 1000) {
            setIsValid(true); // Input is valid
        } else {
            setIsValid(false); // Input is invalid
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const paraIntTitles = parseInt(nTitles, 10)
        const trimed_example = examples.filter((example) => example.trim() !== "")
        handleGenerateSEOTitles(paraIntTitles, pattern, trimed_example, selectedLocation)
    };

    const handleRemoveExample = (index: number) => {
        const newExamples = examples.filter((_, i) => i !== index);
        const updatedTouched = [...hasTouchedExamples];
        setExamples(newExamples);
        updatedTouched.splice(index, 1);
        setHasTouchedExamples(updatedTouched);
    };

    const handleAddExample = () => {
        if (examples.length < 15) {
            setExamples([...examples, ""]);
            setHasTouchedExamples([...hasTouchedExamples, false]);
        }
    };

    const handleBlur = (index) => {
        const updatedTouched = [...hasTouchedExamples];
        updatedTouched[index] = true; // Mark the field as touched
        setHasTouchedExamples(updatedTouched);
    };

    const validateExamples = () => {
        return examples.every((example) => example.trim() !== "");
    };

    function handleGenerateSEOTitles(nTitles, pattern, examples, selectedLocation) {
        setIsSpinner(true)
        generateSeoTitles.mutate({
            n_titles: nTitles, pattern: pattern, example_pattern: examples, selectedLocation: selectedLocation
        }, {
            onSuccess: (response) => {
                if (response.data.status === "rejected") {
                    errorAlertRef.current?.show("Max limit reached to generate Programmatic seo titles.");
                    setIsSpinner(false)
                } else {
                    const taskId = response.data?.task_id;
                    const keywordHash = response.data?.keyword_hash
                    const projectId = response.data?.project_id
                    const pattern = response.data?.pattern
                    setIsSpinner(false)
                    if (!taskId) {
                        errorAlertRef.current?.show("Task ID not found in response.");
                        setTimeout(() => errorAlertRef.current?.close(), 5000);
                        return;
                    }
                    localStorage.setItem(`task_${projectId}`, taskId);
                    localStorage.setItem(`pattern_${projectId}`, pattern);
                    localStorage.setItem(`kwHash_${projectId}`, keywordHash)
                    successAlertRef.current?.show('Query submitted. It will take few minutes to complete.');
                    navigate(`/pseo-list-of-titles/${projectId}/titles/${keywordHash}`);
                    setNTitles("");
                    setPattern("");
                    setExamples(["", "", ""]);
                    setHasTouchedExamples([]);
                }
            },
            onError: () => {
                errorAlertRef.current?.show("Failed to generate title");
                setIsSpinner(false)
                setTimeout(() => errorAlertRef.current?.close(), 3000);
            }
        });

    }


    const validatePattern = (value: string) => {
        const curlyBracketRegex = /\{[^{}]+\}/g;
        const matches = value.match(curlyBracketRegex);

        // Check if there are unmatched or invalid curly braces
        const openBraces = (value.match(/\{/g) || []).length;
        const closeBraces = (value.match(/\}/g) || []).length;

        return matches !== null && matches.length > 0 && openBraces === closeBraces;
    };

    const handlePatternChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setPattern(value);
        setIsPatternValid(validatePattern(value)); // Validate the input
    };

    const handleExampleChange = (index: number, value: string) => {
        const updatedExamples = [...examples];
        updatedExamples[index] = value;
        setExamples(updatedExamples);
    };

    const areFirstThreeFilled = examples.slice(0, 3).every((example) => example.trim() !== "");

    return (
        <>
            {showSurvey && <Survey />}
            <Helmet>
                <title>Create pSEO Articles | Abun.com</title>
                <meta
                  name="description"
                  content="Use structured data to create programmatic SEO articles at scale."
                />
            </Helmet>
            <div className="ps-container">
             <div className="w-100">
                    {/* ------------ Reddit SEO Header ------------ */}
                    <div className="mb-5 is-flex-wrap-wrap ps-header is-flex is-justify-content-space-between">
                        <div className={"is-flex is-justify-content-center is-flex-direction-column "}>
                            <h1 className="ps-title">Programmatic SEO Article Generator</h1>
                            <p className={"pseo-subtext has-text-dark"}>
                                Generate hundreds of SEO-optimized articles in minutes. all targeted around long-tail keywords your audience is searching for.<br/>
                                Perfect for ranking fast, scaling traffic, and dominating search results without writing each post manually.
                            </p>
                        </div>
                        <div className="is-block mt-2">
                            <span>
                                3 Programmatic SEO Credits remaining. <Link to={pageURL["subscriptionCredit"]} className="is-text has-text-black is-underlined" >View Credits</Link>
                            </span>
                        </div>
                    </div>

                    <div className="tabs is-medium" style={{scrollbarWidth:'none'}}>
                        <ul>
                          <li className={activeTab === "programmatic-seo" ? "is-active" : ""}>
                            <a onClick={() => setActiveTab("programmatic-seo")}>Programmatic SEO</a>
                          </li>
                          <li className={activeTab === "projects" ? "is-active" : ""}>
                            <a onClick={() => setActiveTab("projects")}>Projects</a>
                          </li>
                        </ul>
                    </div>
                {activeTab ==="programmatic-seo" && 
                <div className="seo-form-container">
                    <h3 >Let's start creating Programmatic SEO Articles</h3>
                    <hr/>
                    {/* <h2>Generate SEO-Optimized Titles</h2> */}
                    <form onSubmit={handleSubmit} className="seo-form">
                        <div className="form-group bottom-space">
                            {/* <label className="seo-text" htmlFor="nTitles">Total Number of Titles</label> */}
                            <h4 className="" style={{ color: '#2E64FE', textAlign: 'left', fontSize: '1.3em' }}>
                                Enter Title Pattern to Generate Multiple Titles.
                            </h4>
                            <TextField id="pattern" placeholder="Hire {Designer Role} in {location}" label={"Title Pattern"} variant="outlined" sx={{ width: isSmallScreen ? 270 : 375 }} value={pattern} onChange={handlePatternChange} InputLabelProps={{ shrink: true }} />
                            {!isPatternValid && (
                                <small className="error" style={{ color: "red"}}>
                                    Please include at least one valid placeholder in the format {"{example}"}. Ensure text is inside the curly braces.
                                </small>
                            )}
                            <p style={{ color: "rgba(0, 0, 0, 0.5)", fontSize: "1em", marginTop: "-10px" }}>
                                Use &#123;&#125; to define any subject in the title.
                            </p>
                        </div>
                        <div className="form-group" >
                            {/* <label className="seo-text">Examples (at least 3)</label> */}
                            <h4 className="" style={{ color: '#2E64FE', textAlign: 'left', fontSize: '1.3em' }}>
                                Provides a few example of Article Titles<br /> based on above Pattern
                            </h4>
                            <div className="form-group-input-field">
                                {examples.map((example, index) => (
                                    <div className="abcd">
                                        <div key={index} className={`example-input-container bottom-space ${index >= 1 ? "extras" : ""}`}>
                                            <TextField label={examples.length === 1 ? "Example" : `Example ${index + 1}`} placeholder={examplePlaceholders[index] || examplePlaceholders[0]} variant="outlined" sx={{ width: isSmallScreen ? 270 : 375 }} value={example} onChange={(e) => handleExampleChange(index, e.target.value)} onBlur={() => handleBlur(index)} error={hasTouchedExamples[index] && example.trim() === ""} helperText={hasTouchedExamples[index] && example.trim() === "" ? "This field is required" : ""} InputLabelProps={{ shrink: true }} />
                                            {examples.length > 1 && (
                                                <button
                                                    type="button"
                                                    className="remove-btn"
                                                    onClick={() => handleRemoveExample(index)}
                                                >&times;</button>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                            <div className="form-group add-more">
                                <button
                                    type="button"
                                    className="add-btn"
                                    onClick={handleAddExample}
                                    disabled={!areFirstThreeFilled}
                                >
                                    + Add More Examples
                                </button>
                            </div>
                        </div>
                        <div className="form-group bottom-space">
                            <h4 className="" style={{ color: '#2E64FE', textAlign: 'left', fontSize: '1.3em' }}>
                                How many Titles to generate?<br />
                            </h4>
                            <p style={{ color: "rgba(0, 0, 0, 0.5)", fontSize: "1em", marginTop: "-10px" }}>&#123;Your Credit won't get deducted&#125;</p>
                            <TextField id="nTitles" label={"Total Articles Titles to Generate"} placeholder="10" variant="outlined" sx={{ width: isSmallScreen ? 180 : 265 }} value={nTitles} onChange={handleInputChange} InputLabelProps={{ shrink: true }}
                            />
                            {!isValid && (
                                <small className="error" style={{ color: "red" }}>Please enter a number between 1 and 1000.</small>
                            )}
                        </div>
                        <div data-tooltip-id="buttonTip" data-tooltip-content="Verify email to generate the PSEO">
                            <div>
                                <button type="submit" className="button is-size-6 is-responsive is-link" disabled={!isValid || !nTitles || !validateExamples() || !isVerified} >
                                    PROCEED {isSpinner ? <Icon iconName="spinner" additionalClasses={["icon-white", "mr-3"]} /> :  <FontAwesomeIcon icon={faArrowRight} className="ml-2 is-size-6"/>}
                                </button>
                            </div>
                            {!isVerified && (
                                <Tooltip id="buttonTip" place="bottom" />
                            )}
                        </div>
                        {showLoading && (
                            <AbunModal active={showLoading}
                                headerText={""}
                                modelWidth="450px"
                                closeable={false}
                                hideModal={() => {
                                    setShowLoading(false)
                                }}
                            >
                                <div className="loader-container" aria-live="polite" aria-busy={showLoading}
                                    style={{
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        marginTop: '30px'
                                    }}>
                                    {/* ldrs loader */}
                                    <l-quantum size="60" speed="1.75" color="#2E64FE"></l-quantum>
                                </div>
                                <h3 className="modal-header-text">Please hold on</h3>
                                <p className="modal-subtext">Processing request. Please wait...</p>
                            </AbunModal>
                        )}
                        {/* {error && showLoading && (
                            <p
                                style={{
                                    textAlign: "center",
                                    fontSize: "1.3rem",
                                }}
                            >
                                An error occurred.
                                <img
                                    src="/static/media/spinner.a81353c0a4707f9915a4835a359de143.svg"
                                    alt="spinner"
                                    className="spinner-spin ml-5"
                                    style={{
                                        width: "1.4em",
                                        height: "auto",
                                        verticalAlign: "middle",
                                    }}
                                />
                            </p>
                        )} */}
                    </form>
                    <SuccessAlert ref={successAlertRef} style={{ left: '60%' }} />
                    <ErrorAlert ref={errorAlertRef} style={{ left: '60%' }} />
                </div>
                }

                {activeTab === "projects" &&
                    <TitleProject/>
                }
             </div>
            </div>
        </>
    );
};

export default ProgrammaticSeo;